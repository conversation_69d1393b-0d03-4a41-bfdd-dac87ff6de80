import { Tab, Tabs } from 'react-bootstrap'
import { useSearchParams } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'

import LtvTab from './components/LtvTab/LtvTab'
import RetentionTab from './components/RetentionTab/RetentionTab'
import RevenueTab from './components/RevenueTab/RevenueTab'

/**
 * Экран метрик и аналитики
 */
const MetricsScreen = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const activeTab = searchParams.get('tab') || 'revenue'

  const handleTabSelect = (tab) => {
    const newParams = new URLSearchParams(searchParams)
    if (tab) {
      newParams.set('tab', tab)
    } else {
      newParams.delete('tab')
    }
    setSearchParams(newParams)
  }

  return (
    <Layout title="Метрики">
      <Tabs activeKey={activeTab} onSelect={handleTabSelect} className="mb-4">
        <Tab eventKey="revenue" title="Выручка за год">
          <RevenueTab isActive={activeTab === 'revenue'} />
        </Tab>
        <Tab eventKey="retention" title="Retention">
          <RetentionTab isActive={activeTab === 'retention'} />
        </Tab>
        <Tab eventKey="ltv" title="LTV">
          <LtvTab isActive={activeTab === 'ltv'} />
        </Tab>
      </Tabs>
    </Layout>
  )
}

export default MetricsScreen
