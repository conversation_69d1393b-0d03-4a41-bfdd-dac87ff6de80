import { useState, useEffect } from 'react'
import { Card, Col, Form, Row, Table, Alert, ButtonGroup, But<PERSON>, Spinner } from 'react-bootstrap'
import { useSearchParams } from 'react-router-dom'

import { BarChart } from '@/components/Charts'

import { useGetRevenueByYear } from '../../api'

/**
 * Компонент для отображения выручки за год
 */
const RevenueTab = ({ isActive = true }) => {
  const currentYear = new Date().getFullYear()
  const [searchParams, setSearchParams] = useSearchParams()
  const [selectedYear, setSelectedYear] = useState(() => {
    const yearFromUrl = searchParams.get('year')
    return yearFromUrl ? Number(yearFromUrl) : currentYear
  })

  // Синхронизируем URL при изменении года
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams)
    if (selectedYear) {
      newParams.set('year', selectedYear.toString())
    } else {
      newParams.delete('year')
    }
    setSearchParams(newParams)
  }, [selectedYear, searchParams, setSearchParams])

  const { data: revenueResponse, isLoading, error } = useGetRevenueByYear(selectedYear, isActive)
  const revenueData = revenueResponse?.data

  // Генерируем список годов (текущий год и 4 года назад)
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const getChartData = () => {
    if (!revenueData?.months) return null

    return {
      labels: revenueData.months.map((month) => month.name),
      datasets: [
        {
          label: 'Выручка',
          data: revenueData.months.map((month) => month.revenue),
          backgroundColor: 'rgba(59, 130, 246, 0.8)',
          borderColor: '#3b82f6',
          hoverBackgroundColor: 'rgba(59, 130, 246, 0.9)',
        },
      ],
    }
  }

  return (
    <div>
      <Row className="mb-4">
        <Col md={4}>
          <Form.Group>
            <Form.Label>Выберите год</Form.Label>
            <ButtonGroup className="d-flex flex-wrap gap-2">
              {years.map((year) => (
                <Button
                  key={year}
                  variant={selectedYear === year ? 'primary' : 'outline-primary'}
                  size="sm"
                  onClick={() => setSelectedYear(year)}
                >
                  {year}
                </Button>
              ))}
            </ButtonGroup>
          </Form.Group>
        </Col>
      </Row>

      {isLoading ? (
        <div className="text-center py-5">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Загрузка...</span>
          </Spinner>
        </div>
      ) : (
        <>
          {error && <Alert variant="danger">Ошибка при загрузке данных: {error.message}</Alert>}

          {revenueData && revenueData.total_revenue > 0 && (
            <>
              {/* График выручки на всю ширину */}
              <Row className="mb-4">
                <Col>
                  <BarChart
                    data={getChartData()}
                    title={`Выручка по месяцам за ${selectedYear} год`}
                    height={400}
                    formatValue={formatCurrency}
                    colors={{
                      primary: '#3b82f6',
                      primaryLight: 'rgba(59, 130, 246, 0.8)',
                      hover: 'rgba(59, 130, 246, 0.9)',
                    }}
                  />
                </Col>
              </Row>

              {/* Карточка с общей выручкой и таблица рядом */}
              <Row>
                <Col lg={4}>
                  <Card className="bg-primary text-white h-100">
                    <Card.Body className="d-flex flex-column justify-content-center">
                      <div className="text-center">
                        <div className="fs-1 opacity-75 mb-3">
                          <i className="bi bi-graph-up" />
                        </div>
                        <h6 className="text-white-50 mb-2">Общая выручка за {selectedYear} год</h6>
                        <h2 className="mb-0 fw-bold">{formatCurrency(revenueData.total_revenue)}</h2>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
                <Col lg={8}>
                  <Card className="h-100">
                    <Card.Header>
                      <h5 className="mb-0">Детализация по месяцам</h5>
                    </Card.Header>
                    <Card.Body>
                      <Table responsive striped hover className="mb-0">
                        <thead>
                          <tr>
                            <th>Месяц</th>
                            <th className="text-end">Выручка</th>
                          </tr>
                        </thead>
                        <tbody>
                          {revenueData.months?.map((month, index) => (
                            <tr key={index}>
                              <td>{month.name}</td>
                              <td className="text-end">{formatCurrency(month.revenue)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            </>
          )}

          {revenueData && revenueData.total_revenue === 0 && (
            <Alert variant="info" className="text-center">
              <h5>Нет данных о выручке за {selectedYear} год</h5>
              <p className="mb-3">За выбранный год отсутствуют данные о выручке. Попробуйте выбрать другой год.</p>
            </Alert>
          )}
        </>
      )}
    </div>
  )
}

export default RevenueTab
