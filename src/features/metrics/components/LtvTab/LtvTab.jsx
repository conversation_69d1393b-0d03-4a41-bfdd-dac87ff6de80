import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, <PERSON>, Form, ButtonGroup, Button, Table } from 'react-bootstrap'
import { useSearchParams } from 'react-router-dom'

import { LineChart } from '@/components/Charts'

import { useGetLtvData, useGetLtvByYear } from '../../api'
import MetricsFilters from '../shared/MetricsFilters'

/**
 * Компонент для отображения данных LTV (Lifetime Value)
 */
const LtvTab = ({ isActive = true }) => {
  const currentYear = new Date().getFullYear()
  const [searchParams, setSearchParams] = useSearchParams()

  // Состояние для выбора года для графика
  const [selectedYear, setSelectedYear] = useState(() => {
    const yearFromUrl = searchParams.get('year')
    return yearFromUrl ? Number(yearFromUrl) : currentYear
  })

  // Синхронизируем URL при изменении года
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams)
    if (selectedYear) {
      newParams.set('year', selectedYear.toString())
    } else {
      newParams.delete('year')
    }
    setSearchParams(newParams)
  }, [selectedYear, searchParams, setSearchParams])

  // Вычисляем даты для пресета "Последние 12 месяцев" (активен по умолчанию)
  const getDefaultDates = () => {
    const now = new Date()
    const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
    return {
      start_date: oneYearAgo.toISOString().split('T')[0],
      end_date: now.toISOString().split('T')[0],
    }
  }

  const defaultDates = getDefaultDates()

  const [formData, setFormData] = useState({
    ...defaultDates,
    event_type_public_id: null,
  })
  const [selectedPreset, setSelectedPreset] = useState('last_12_months')

  // API для общих данных LTV
  const { mutate: getLtvData, data: ltvResponse, isLoading, error } = useGetLtvData()
  const ltvData = ltvResponse?.data

  // API для данных LTV по месяцам за год
  const { data: ltvYearResponse, isLoading: isLoadingYear, error: errorYear } = useGetLtvByYear(selectedYear, isActive)
  const ltvYearData = ltvYearResponse?.data

  const formatNumber = (number) => {
    return number.toLocaleString('ru-RU')
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      maximumFractionDigits: 2,
    }).format(amount)
  }

  const calculateLtv = (totalRevenue, uniqueUsers) => {
    if (uniqueUsers === 0) return 0
    return Math.round((totalRevenue / uniqueUsers) * 100) / 100
  }

  // Генерируем список годов (текущий год и 4 года назад)
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  // Функция для получения данных графика
  const getChartData = () => {
    if (!ltvYearData?.values) return null

    return {
      labels: ltvYearData.values.map((month) => month.name),
      datasets: [
        {
          label: 'Средний LTV',
          data: ltvYearData.values.map((month) => month.avg_ltv),
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          yAxisID: 'y', // Левая ось для LTV
        },
        {
          label: 'Количество пользователей',
          data: ltvYearData.values.map((month) => month.users),
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          yAxisID: 'y1', // Правая ось для пользователей
        },
      ],
    }
  }

  const handleFormDataChange = (newFormData) => {
    setFormData(newFormData)
  }

  const handlePresetSelect = (presetKey) => {
    setSelectedPreset(presetKey)
  }

  /**
   * Сброс фильтров к значениям по умолчанию (последние 12 месяцев)
   */
  const resetFilters = () => {
    const now = new Date()
    const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())

    setFormData({
      start_date: oneYearAgo.toISOString().split('T')[0],
      end_date: now.toISOString().split('T')[0],
      event_type_public_id: null,
    })
    setSelectedPreset('last_12_months')
  }

  /**
   * Обработчик отправки формы
   */
  const handleSubmit = () => {
    const requestData = {
      start_date: new Date(formData.start_date).toISOString(),
      end_date: new Date(formData.end_date).toISOString(),
      event_type_public_id: formData.event_type_public_id,
    }
    getLtvData(requestData)
  }

  return (
    <div>
      <h3 className="mb-3">Покупки</h3>

      {/* Фильтры для общих данных LTV */}
      <MetricsFilters
        formData={formData}
        onInputChange={handleFormDataChange}
        onSubmit={handleSubmit}
        onResetFilters={resetFilters}
        selectedPreset={selectedPreset}
        onPresetSelect={handlePresetSelect}
        isLoading={isLoading}
      />

      {/* Общие данные LTV */}
      <div className="mb-5">
        {isLoading ? (
          <div className="text-center py-5">
            <Spinner animation="border" role="status">
              <span className="visually-hidden">Загрузка общих данных...</span>
            </Spinner>
          </div>
        ) : (
          <>
            {error && <Alert variant="danger">Ошибка при загрузке общих данных: {error.message}</Alert>}

            {ltvData && (
              <Row className="g-4">
                <Col md={4}>
                  <Card className="bg-primary text-white">
                    <Card.Body>
                      <div className="d-flex justify-content-between align-items-center">
                        <div>
                          <h6 className="text-white-50 mb-1">Общая выручка</h6>
                          <h3 className="mb-0">{formatCurrency(ltvData.total_revenue)}</h3>
                        </div>
                        <div className="fs-1 opacity-50">
                          <i className="bi bi-cash-stack" />
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={4}>
                  <Card className="bg-info text-white">
                    <Card.Body>
                      <div className="d-flex justify-content-between align-items-center">
                        <div>
                          <h6 className="text-white-50 mb-1">Пользователи</h6>
                          <h3 className="mb-0">{formatNumber(ltvData.unique_users)}</h3>
                        </div>
                        <div className="fs-1 opacity-50">
                          <i className="bi bi-people" />
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={4}>
                  <Card className="bg-success text-white">
                    <Card.Body>
                      <div className="d-flex justify-content-between align-items-center">
                        <div>
                          <h6 className="text-white-50 mb-1">LTV</h6>
                          <h3 className="mb-0">
                            {formatCurrency(calculateLtv(ltvData.total_revenue, ltvData.unique_users))}
                          </h3>
                        </div>
                        <div className="fs-1 opacity-50">
                          <i className="bi bi-graph-up-arrow" />
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            )}

            {ltvData && ltvData.unique_users === 0 && (
              <Alert variant="info" className="text-center">
                <h5>Нет данных за выбранный период</h5>
                <p className="mb-3">Попробуйте изменить период или тип события для получения результатов.</p>
              </Alert>
            )}
          </>
        )}
      </div>

      <h3 className="mb-3">Когорты</h3>

      {/* Выбор года для графика */}
      <Row className="mb-4">
        <Col md={4}>
          <Form.Group>
            <Form.Label>Выберите год для анализа по месяцам</Form.Label>
            <ButtonGroup className="d-flex flex-wrap gap-2">
              {years.map((year) => (
                <Button
                  key={year}
                  variant={selectedYear === year ? 'primary' : 'outline-primary'}
                  size="sm"
                  onClick={() => setSelectedYear(year)}
                >
                  {year}
                </Button>
              ))}
            </ButtonGroup>
          </Form.Group>
        </Col>
      </Row>

      {/* График LTV по месяцам */}
      {isLoadingYear ? (
        <div className="text-center py-5">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Загрузка графика...</span>
          </Spinner>
        </div>
      ) : (
        <>
          {errorYear && <Alert variant="danger">Ошибка при загрузке данных графика: {errorYear.message}</Alert>}

          {ltvYearData && ltvYearData.values && ltvYearData.values.some((month) => month.avg_ltv > 0) && (
            <>
              {/* График LTV на всю ширину */}
              <Row className="mb-4">
                <Col>
                  <LineChart
                    data={getChartData()}
                    title={`LTV и количество пользователей по месяцам за ${selectedYear} год`}
                    height={400}
                    showLegend={true}
                    dualYAxis={true}
                    colors={{
                      primary: '#10b981',
                      primaryLight: 'rgba(16, 185, 129, 0.1)',
                      secondary: '#3b82f6',
                      secondaryLight: 'rgba(59, 130, 246, 0.1)',
                    }}
                  />
                </Col>
              </Row>

              {/* Таблица с детализацией */}
              <Row className="mb-4">
                <Col>
                  <Card>
                    <Card.Header>
                      <h5 className="mb-0">Детализация LTV по месяцам за {selectedYear} год</h5>
                    </Card.Header>
                    <Card.Body>
                      <Table responsive striped hover className="mb-0">
                        <thead>
                          <tr>
                            <th>Месяц</th>
                            <th className="text-end">Средний LTV</th>
                            <th className="text-end">Количество пользователей</th>
                          </tr>
                        </thead>
                        <tbody>
                          {ltvYearData.values?.map((month, index) => (
                            <tr key={index}>
                              <td>{month.name}</td>
                              <td className="text-end">{month.avg_ltv > 0 ? formatCurrency(month.avg_ltv) : '—'}</td>
                              <td className="text-end">{month.users > 0 ? formatNumber(month.users) : '—'}</td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            </>
          )}

          {ltvYearData && ltvYearData.values && !ltvYearData.values.some((month) => month.avg_ltv > 0) && (
            <Alert variant="info" className="text-center">
              <h5>Нет данных LTV за {selectedYear} год</h5>
              <p className="mb-3">За выбранный год отсутствуют данные о LTV. Попробуйте выбрать другой год.</p>
            </Alert>
          )}
        </>
      )}
    </div>
  )
}

export default LtvTab
