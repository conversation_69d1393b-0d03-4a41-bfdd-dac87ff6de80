import { useState } from 'react'
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, But<PERSON> } from 'react-bootstrap'

import { useGetRetentionData } from '../../api'
import MetricsFilters from '../shared/MetricsFilters'

/**
 * Компонент для отображения данных retention
 */
const RetentionTab = ({ isActive = true }) => {
  // Вычисляем даты для пресета "Последние 12 месяцев" (активен по умолчанию)
  const getDefaultDates = () => {
    const now = new Date()
    const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
    return {
      start_date: oneYearAgo.toISOString().split('T')[0],
      end_date: now.toISOString().split('T')[0],
    }
  }

  const defaultDates = getDefaultDates()

  const [formData, setFormData] = useState({
    ...defaultDates,
    event_type_public_id: null,
  })
  const [selectedPreset, setSelectedPreset] = useState('last_12_months')

  const { mutate: getRetentionData, data: retentionResponse, isLoading, error } = useGetRetentionData()
  const retentionData = retentionResponse?.data

  const formatNumber = (number) => {
    return number.toLocaleString('ru-RU')
  }

  const formatPercent = (value) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'percent',
      maximumFractionDigits: 2,
    }).format(value / 100)
  }

  const handleFormDataChange = (newFormData) => {
    setFormData(newFormData)
  }

  const handlePresetSelect = (presetKey) => {
    setSelectedPreset(presetKey)
  }

  /**
   * Сброс фильтров к значениям по умолчанию (последние 12 месяцев)
   */
  const resetFilters = () => {
    const now = new Date()
    const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())

    setFormData({
      start_date: oneYearAgo.toISOString().split('T')[0],
      end_date: now.toISOString().split('T')[0],
      event_type_public_id: null,
    })
    setSelectedPreset(null)
  }

  /**
   * Обработчик отправки формы
   */
  const handleSubmit = () => {
    const requestData = {
      start_date: new Date(formData.start_date).toISOString(),
      end_date: new Date(formData.end_date).toISOString(),
      event_type_public_id: formData.event_type_public_id,
    }
    getRetentionData(requestData)
  }

  const calculateRetentionRate = (repeatUsers, firstTimeUsers) => {
    if (firstTimeUsers === 0) return 0
    return (repeatUsers / firstTimeUsers) * 100
  }

  return (
    <div>
      <MetricsFilters
        formData={formData}
        onInputChange={handleFormDataChange}
        onSubmit={handleSubmit}
        onResetFilters={resetFilters}
        selectedPreset={selectedPreset}
        onPresetSelect={handlePresetSelect}
        isLoading={isLoading}
        submitButtonText="Получить данные"
      />

      {isLoading ? (
        <div className="text-center py-5">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Загрузка...</span>
          </Spinner>
        </div>
      ) : (
        <>
          {error && <Alert variant="danger">Ошибка при загрузке данных: {error.message}</Alert>}

          {retentionData && retentionData.total_users > 0 && (
            <Row className="g-4">
              <Col md={4}>
                <Card className="bg-info text-white">
                  <Card.Body>
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <h6 className="text-white-50 mb-1">Одна покупка</h6>
                        <h3 className="mb-0">{formatNumber(retentionData.first_time_users)}</h3>
                      </div>
                      <div className="fs-1 opacity-50">
                        <i className="bi bi-person-plus" />
                      </div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={4}>
                <Card className="bg-success text-white">
                  <Card.Body>
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <h6 className="text-white-50 mb-1">Повторные покупки</h6>
                        <h3 className="mb-0">{formatNumber(retentionData.repeat_users)}</h3>
                      </div>
                      <div className="fs-1 opacity-50">
                        <i className="bi bi-arrow-repeat" />
                      </div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={4}>
                <Card className="bg-warning text-dark">
                  <Card.Body>
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <h6 className="text-muted mb-1">Retention</h6>
                        <h3 className="mb-0">
                          {formatPercent(
                            calculateRetentionRate(retentionData.repeat_users, retentionData.first_time_users)
                          )}
                        </h3>
                      </div>
                      <div className="fs-1 opacity-50">
                        <i className="bi bi-percent" />
                      </div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          )}

          {retentionData && retentionData.total_users === 0 && (
            <Alert variant="info" className="text-center">
              <h5>Нет данных за выбранный период</h5>
              <p className="mb-3">Попробуйте изменить период или тип события для получения результатов.</p>
              <Button variant="outline-primary" onClick={resetFilters}>
                Сбросить фильтры
              </Button>
            </Alert>
          )}
        </>
      )}
    </div>
  )
}

export default RetentionTab
