import { useMemo } from 'react'
import { Card, Col, Form, Row, Button, ButtonGroup } from 'react-bootstrap'

import { useGetEventTypeList } from '@/features/events/api/getEventTypeList'

const DATE_PRESETS = [
  { key: 'last_7_days', label: 'Последние 7 дней', days: 7 },
  { key: 'last_30_days', label: 'Последние 30 дней', days: 30 },
  { key: 'year_to_date', label: 'С начала года', yearStart: true },
  { key: 'last_12_months', label: 'Последние 12 мес.', months: 12 },
  { key: 'all_history', label: 'За всю историю', allHistory: true },
]

/**
 * Компонент фильтров для метрик и аналитики
 * Предоставляет общие фильтры: период дат, тип события
 */
const MetricsFilters = ({
  formData,
  onInputChange,
  onSubmit,
  onResetFilters,
  selectedPreset,
  onPresetSelect,
  isLoading = false,
  submitButtonText = 'Получить данные',
}) => {
  const { data: eventTypesResponse } = useGetEventTypeList()
  const eventTypes = eventTypesResponse?.data?.values || []

  // Валидация дат - только проверяем, что дата начала не больше даты окончания
  const isValidDateRange = useMemo(() => {
    const startDate = new Date(formData.start_date)
    const endDate = new Date(formData.end_date)

    return startDate <= endDate
  }, [formData.start_date, formData.end_date])

  /**
   * Применяет пресет дат
   * @param {object} preset - Тип пресета
   */
  const applyDatePreset = (preset) => {
    const now = new Date()
    let startDate = new Date(now)

    if (preset.days) {
      startDate.setDate(now.getDate() - preset.days)
    } else if (preset.months) {
      startDate.setMonth(now.getMonth() - preset.months)
    } else if (preset.yearStart) {
      startDate.setMonth(0, 1) // начало текущего года
    } else if (preset.allHistory) {
      startDate = new Date('2021-01-01') // начало истории - 01.01.2021
    }

    const newFormData = {
      ...formData,
      start_date: startDate.toISOString().split('T')[0],
      end_date: now.toISOString().split('T')[0],
    }

    onInputChange(newFormData)
    onPresetSelect(preset.key)
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    const newFormData = {
      ...formData,
      [name]: value === '' ? null : value,
    }

    onInputChange(newFormData)

    // Сбрасываем выбранный пресет при ручном изменении дат
    if (name === 'start_date' || name === 'end_date') {
      onPresetSelect(null)
    }
  }

  const handleSubmit = () => {
    if (!isValidDateRange) return
    onSubmit()
  }

  return (
    <Card className="mb-4">
      <Card.Header>
        <h5 className="mb-0">Параметры анализа</h5>
      </Card.Header>
      <Card.Body>
        {/* Пресеты периодов */}
        <Row className="mb-3">
          <Col>
            <Form.Label className="mb-2">Быстрый выбор периода:</Form.Label>
            <ButtonGroup className="d-flex flex-wrap gap-2">
              {DATE_PRESETS.map((preset) => (
                <Button
                  key={preset.key}
                  variant={selectedPreset === preset.key ? 'primary' : 'outline-primary'}
                  size="sm"
                  onClick={() => applyDatePreset(preset)}
                >
                  {preset.label}
                </Button>
              ))}
            </ButtonGroup>
          </Col>
        </Row>

        <Row className="g-3">
          <Col md={3}>
            <Form.Group>
              <Form.Label>Дата начала</Form.Label>
              <Form.Control
                type="date"
                name="start_date"
                value={formData.start_date}
                onChange={handleInputChange}
                isInvalid={!isValidDateRange}
              />
              <Form.Control.Feedback type="invalid">
                Дата начала не может быть больше даты окончания
              </Form.Control.Feedback>
            </Form.Group>
          </Col>
          <Col md={3}>
            <Form.Group>
              <Form.Label>Дата окончания</Form.Label>
              <Form.Control
                type="date"
                name="end_date"
                value={formData.end_date}
                min={formData.start_date}
                onChange={handleInputChange}
                isInvalid={!isValidDateRange}
              />
              <Form.Control.Feedback type="invalid">
                Дата окончания не может быть меньше даты начала
              </Form.Control.Feedback>
            </Form.Group>
          </Col>
          <Col md={3}>
            <Form.Group>
              <Form.Label>Тип события</Form.Label>
              <Form.Select
                name="event_type_public_id"
                value={formData.event_type_public_id || ''}
                onChange={handleInputChange}
              >
                <option value="">Все типы</option>
                {eventTypes.map((eventType) => (
                  <option key={eventType.public_id} value={eventType.public_id}>
                    {eventType.title}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={3} className="d-flex align-items-end">
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={isLoading || !isValidDateRange}
              className="w-100"
            >
              {isLoading && <span className="spinner-border spinner-border-sm me-2" />}
              {submitButtonText}
            </Button>
          </Col>
        </Row>

        {onResetFilters && (
          <Row className="mt-3">
            <Col>
              <Button variant="outline-secondary" size="sm" onClick={onResetFilters}>
                Сбросить фильтры
              </Button>
            </Col>
          </Row>
        )}
      </Card.Body>
    </Card>
  )
}

export default MetricsFilters
