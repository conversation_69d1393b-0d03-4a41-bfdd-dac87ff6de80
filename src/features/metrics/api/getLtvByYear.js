import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const getLtvByYear = (year) => {
  return axios.get(`${APIRoute.ANALYTICS_LTV_YEAR}/${year}`)
}

export const useGetLtvByYear = (year, enabled = true) => {
  return useQuery({
    queryKey: ['ltvByYear', year],
    queryFn: () => getLtvByYear(year),
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    enabled: !!year && enabled,
  })
}
