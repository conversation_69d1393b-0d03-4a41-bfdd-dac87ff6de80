import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const getRevenueByYear = (year) => {
  return axios.get(`${APIRoute.ANALYTICS_REVENUE_YEAR}/${year}`)
}

export const useGetRevenueByYear = (year, enabled = true) => {
  return useQuery({
    queryKey: ['revenueByYear', year],
    queryFn: () => getRevenueByYear(year),
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    enabled: !!year && enabled,
  })
}
