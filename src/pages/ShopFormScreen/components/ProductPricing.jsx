import { Col, FloatingLabel, FormControl, Row, Form, Alert } from 'react-bootstrap'

function ProductPricing({
  formData,
  defaultFormData,
  handleChangeField,
  handleChangeOldPrice,
  handleToggleOldPrice,
  isOldPrice,
  getFieldValue,
}) {
  return (
    <>
      <Alert variant="warning" className="mb-3">
        Все цены на сайте указаны с включенным в стоимость НДС
      </Alert>
      <Row className="g-3">
        <Col md={6}>
          <FloatingLabel controlId="priceProductLabel" label="Цена товара в рублях *">
            <FormControl
              onChange={handleChangeField}
              value={getFieldValue('price', '')}
              name="price"
              type="number"
              placeholder="Цена товара в рублях"
              min="0"
              step="0.01"
              required
            />
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="priorityNumberProductLabel" label="Приоритет">
            <FormControl
              onChange={handleChangeField}
              value={getFieldValue('priority_number', '')}
              name="priority_number"
              type="number"
              placeholder="Приоритет"
              min="0"
            />
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <div className="d-flex align-items-center h-100 pt-2">
            <Form.Check
              onChange={handleToggleOldPrice}
              name="old_price"
              type="switch"
              id="old-price-switch"
              label="Показывать скидку"
              checked={Boolean(isOldPrice)}
              className="me-3"
            />
            {isOldPrice && (
              <div style={{ flex: 1 }}>
                <FloatingLabel controlId="oldPriceProductLabel" label="Старая цена">
                  <FormControl
                    onChange={handleChangeOldPrice}
                    value={formData.old_price || defaultFormData.old_price || ''}
                    name="old_price"
                    type="number"
                    placeholder="Старая цена"
                    min="0"
                    step="0.01"
                  />
                </FloatingLabel>
              </div>
            )}
          </div>
        </Col>
      </Row>
    </>
  )
}

export default ProductPricing
