import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js'
import { useState, useRef } from 'react'
import { Line } from 'react-chartjs-2'

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler)

function LineChart({
  data,
  title = 'График',
  height = 450,
  showLegend = true,
  showGrid = true,
  tension = 0.4,
  fill = true,
  stepSize = null,
  dualYAxis = false,
  colors = {
    primary: '#3b82f6',
    primaryLight: 'rgba(59, 130, 246, 0.1)',
    secondary: '#10b981',
    secondaryLight: 'rgba(16, 185, 129, 0.1)',
  },
}) {
  const chartRef = useRef(null)
  const [hiddenDatasets, setHiddenDatasets] = useState(new Set())

  if (!data || !data.labels || !data.datasets) {
    return (
      <div className="bg-white rounded-3 shadow-sm border p-4 my-3">
        <div
          className="d-flex align-items-center justify-content-center bg-light rounded-2 border-2 border-dashed text-muted fw-medium"
          style={{ height: '300px', fontSize: '16px' }}
        >
          Нет данных для отображения
        </div>
      </div>
    )
  }

  const toggleDataset = (index) => {
    const chart = chartRef.current
    if (chart) {
      const meta = chart.getDatasetMeta(index)
      meta.hidden = meta.hidden === null ? !chart.data.datasets[index].hidden : null
      chart.update()

      const newHiddenDatasets = new Set(hiddenDatasets)
      if (meta.hidden) {
        newHiddenDatasets.add(index)
      } else {
        newHiddenDatasets.delete(index)
      }
      setHiddenDatasets(newHiddenDatasets)
    }
  }

  const enhancedData = {
    ...data,
    datasets: data.datasets.map((dataset, index) => ({
      ...dataset,
      borderColor: dataset.borderColor || (index === 0 ? colors.primary : colors.secondary),
      backgroundColor: dataset.backgroundColor || (index === 0 ? colors.primaryLight : colors.secondaryLight),
      borderWidth: 3,
      pointRadius: 6,
      pointHoverRadius: 8,
      pointBackgroundColor: dataset.borderColor || (index === 0 ? colors.primary : colors.secondary),
      pointBorderColor: '#ffffff',
      pointBorderWidth: 2,
      pointHoverBackgroundColor: '#ffffff',
      pointHoverBorderColor: dataset.borderColor || (index === 0 ? colors.primary : colors.secondary),
      pointHoverBorderWidth: 3,
      tension: tension,
      fill: fill,
      yAxisID: dualYAxis && index === 1 ? 'y1' : 'y',
    })),
  }

  // Базовые настройки для осей Y
  const baseYAxisConfig = {
    display: true,
    beginAtZero: true,
    grid: {
      display: showGrid,
      color: 'rgba(156, 163, 175, 0.2)',
      drawBorder: false,
    },
    ticks: {
      color: '#6b7280',
      font: {
        size: 12,
      },
      callback: function (value) {
        return Number.isInteger(value) ? value : ''
      },
    },
    border: {
      display: false,
    },
  }

  // Настройки осей Y в зависимости от режима
  const scalesConfig = {
    x: {
      display: true,
      grid: {
        display: showGrid,
        color: 'rgba(156, 163, 175, 0.2)',
        drawBorder: false,
      },
      ticks: {
        color: '#6b7280',
        font: {
          size: 12,
        },
        maxRotation: 45,
        minRotation: 0,
      },
      border: {
        display: false,
      },
    },
    y: {
      ...baseYAxisConfig,
      position: dualYAxis ? 'left' : 'left',
      ticks: {
        ...baseYAxisConfig.ticks,
        stepSize: dualYAxis ? null : stepSize,
      },
    },
  }

  if (dualYAxis) {
    scalesConfig.y1 = {
      ...baseYAxisConfig,
      position: 'right',
      grid: {
        drawOnChartArea: false,
      },
      ticks: {
        ...baseYAxisConfig.ticks,
      },
    }
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    resizeDelay: 0,
    aspectRatio: 1,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    layout: {
      padding: {
        top: 10,
        right: 10,
        bottom: 10,
        left: 10,
      },
    },
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.95)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: 'rgba(59, 130, 246, 0.3)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        padding: 12,
        titleFont: {
          size: 14,
          weight: '600',
        },
        bodyFont: {
          size: 13,
        },
        callbacks: {
          title: function (context) {
            return context[0].label
          },
          label: function (context) {
            const label = context.dataset.label || ''
            const value = context.parsed.y
            return `${label}: ${value}`
          },
        },
      },
    },
    scales: scalesConfig,
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
  }

  return (
    <div
      className="bg-white rounded-3 shadow-sm border p-4 my-3"
      style={{
        width: '100%',
        minWidth: '800px',
        display: 'block',
      }}
    >
      {/* Кастомный заголовок */}
      {title && (
        <div className="text-center mb-3">
          <h3
            className="mb-0"
            style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#111827',
              margin: 0,
            }}
          >
            {title}
          </h3>
        </div>
      )}

      {/* Кастомная легенда */}
      {showLegend && (
        <div className="d-flex justify-content-center align-items-center mb-3 gap-4">
          {data.datasets.map((dataset, index) => {
            const isVisible = !hiddenDatasets.has(index)
            const color = dataset.borderColor || (index === 0 ? colors.primary : colors.secondary)

            return (
              <div
                key={index}
                className="d-flex align-items-center"
                onClick={() => toggleDataset(index)}
                style={{
                  cursor: 'pointer',
                  userSelect: 'none',
                  transition: 'opacity 0.2s ease',
                  opacity: isVisible ? 1 : 0.6,
                }}
              >
                {/* Кастомный чекбокс */}
                <div
                  className="me-2 d-flex align-items-center justify-content-center"
                  style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '3px',
                    border: `2px solid ${color}`,
                    backgroundColor: isVisible ? color : 'transparent',
                    transition: 'all 0.2s ease',
                    position: 'relative',
                  }}
                >
                  {isVisible && (
                    <svg width="10" height="8" viewBox="0 0 10 8" style={{ position: 'absolute' }}>
                      <path
                        d="M1 4L3.5 6.5L9 1"
                        stroke="white"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        fill="none"
                      />
                    </svg>
                  )}
                </div>

                {/* Цветная линия-индикатор */}
                <div
                  className="me-2"
                  style={{
                    width: '20px',
                    height: '3px',
                    backgroundColor: color,
                    borderRadius: '2px',
                  }}
                />

                {/* Текст легенды */}
                <span
                  style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    color: isVisible ? '#374151' : '#9ca3af',
                    transition: 'color 0.2s ease',
                  }}
                >
                  {dataset.label}
                </span>
              </div>
            )
          })}
        </div>
      )}

      <div
        style={{
          height: `${height}px`,
          width: '100%',
          minWidth: '800px',
          position: 'relative',
          display: 'block',
        }}
      >
        <style>
          {`
            .chart-container canvas {
              width: 100% !important;
              max-width: none !important;
              min-width: 800px !important;
              height: ${height}px !important;
            }
          `}
        </style>
        <div className="chart-container" style={{ width: '100%', height: '100%' }}>
          <Line ref={chartRef} data={enhancedData} options={options} />
        </div>
      </div>
    </div>
  )
}

export default LineChart
